# 测试环境：只在打包时使用
NODE_ENV=production

VITE_DEV=false

# 请求路径
VITE_BASE_URL='https://test.portal.v2.olp.credat.com.cn'

# 接口地址
VITE_API_URL=/app-api

# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持S3服务
VITE_UPLOAD_TYPE=server

# 文件预览前缀url
#VITE_PREVIEW_PREFIX='https://test.portal.v2.olp.credat.com.cn/admin-api/preview/onlinePreview'
#VITE_PREVIEW_PREFIX='https://file.kkview.cn/onlinePreview'
VITE_PREVIEW_PREFIX='https://preview.credat.com.cn/onlinePreview'


# 是否删除debugger
VITE_DROP_DEBUGGER=true

# 是否删除console.log
VITE_DROP_CONSOLE=true

# 是否sourcemap
VITE_SOURCEMAP=false

# 打包路径
VITE_BASE_PATH=/

# 输出路径
VITE_OUT_DIR=dist/olp-portal

# 商城H5会员端域名
VITE_MALL_H5_DOMAIN='http://mall.yudao.iocoder.cn'

# 统计分析
VITE_COUNTLY_APP_KEY=5daf0735c5c2fb3aec1b50c7c0cca322d483d92a
VITE_COUNTLY_URL='http://10.248.18.8:80'
VITE_COUNTLY_DEBUG=true
