<template>
  <ContainerWrapper>
    <template #content>
      <ContainerScroll>
        <template #header>
          <div class="w-full h-full px-4 flex items-center justify-between">
            <div>
              <h1 class="text-xl font-semibold">Onboarding</h1>
            </div>

            <!-- 右上角：Mode 和数据统计 -->
            <div class="flex items-center gap-6">
              <!-- Learning Mode -->
              <Badge
                v-if="!loading && onboardingMap"
                :variant="
                  onboardingMap.mode === OnboardingMapModeEnum.FREE ? 'secondary' : 'default'
                "
                class="text-xs"
              >
                {{
                  onboardingMap.mode === OnboardingMapModeEnum.FREE
                    ? 'Free Learning'
                    : 'Step by Step'
                }}
              </Badge>

              <!-- 数据统计 -->
              <div v-if="!loading && onboardingMap?.nodes" class="flex items-center gap-4">
                <div class="text-center">
                  <div class="text-lg font-bold text-primary">{{ getCompletedNodesCount() }}</div>
                  <div class="text-xs text-muted-foreground">Completed</div>
                </div>
                <div class="text-center">
                  <div class="text-lg font-bold text-orange-600">{{
                    getInProgressNodesCount()
                  }}</div>
                  <div class="text-xs text-muted-foreground">In Progress</div>
                </div>
                <div class="text-center">
                  <div class="text-lg font-bold text-muted-foreground">{{
                    getTotalNodesCount()
                  }}</div>
                  <div class="text-xs text-muted-foreground">Total</div>
                </div>
                <div class="ml-2">
                  <div class="text-xs text-muted-foreground mb-1">Progress</div>
                  <div class="flex items-center gap-2">
                    <Progress :model-value="getOverallProgressPercentage()" class="h-2 w-20" />
                    <span class="text-xs font-medium">{{ getOverallProgressPercentage() }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>

        <!-- 内容区域 -->
        <div v-if="loading" class="flex items-center justify-center h-64">
          <div class="text-center">
            <div
              class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"
            ></div>
            <p class="text-sm text-muted-foreground">Loading learning map...</p>
          </div>
        </div>

        <div v-else class="p-6">
          <Stepper
            v-for="(nodes, groupIndex) in Object.values(nodeGroupMap)"
            :key="groupIndex"
            class="relative"
            :class="{ 'flex-row-reverse': groupIndex % 2 !== 0 }"
            v-model="step"
          >
            <StepperItem
              v-for="(node, index) in nodes"
              :key="node.id"
              class="basis-1/4"
              :completed="node.status === OnboardingMapNodeStatusEnum.COMPLETED"
              :disabled="
                onboardingMap.mode === OnboardingMapModeEnum.STAGE &&
                getPreviousNodeStatus(node.id!) !== OnboardingMapNodeStatusEnum.COMPLETED
              "
              :step="node.sort"
            >
              <StepperLine
                v-if="
                  (groupIndex % 2 === 0 && index === 0) ||
                  (groupIndex % 2 !== 0 && index === nodes.length - 1)
                "
                :class="{ 'bg-muted': groupIndex === 0 && index === 0 }"
              />
              <!--        <StepperSeparator-->
              <!--          v-if="-->
              <!--            (groupIndex % 2 === 0 && index === 0) ||-->
              <!--            (groupIndex % 2 !== 0 && index === nodes.length - 1)-->
              <!--          "-->
              <!--          class="w-full h-px group-data-disabled:bg-primary"-->
              <!--        />-->
              <StepperTrigger>
                <StepperIndicator>
                  <Icon :name="node.icon" class="w-4 h-4" />
                </StepperIndicator>
                <div class="flex flex-col">
                  <Sheet>
                    <SheetTrigger as-child>
                      <StepperTitle
                        class="hover:underline cursor-pointer"
                        :class="{
                          'text-foreground/30':
                            onboardingMap.mode === OnboardingMapModeEnum.STAGE &&
                            getPreviousNodeStatus(node.id!) !==
                              OnboardingMapNodeStatusEnum.COMPLETED
                        }"
                      >
                        {{ node.name }}
                      </StepperTitle>
                    </SheetTrigger>

                    <SheetContent class="w-[500px] sm:w-[600px] flex flex-col p-0">
                      <SheetHeader class="px-6 py-6 border-b bg-muted/30">
                        <div class="flex items-start gap-4">
                          <!-- 节点图标 -->
                          <div
                            class="w-14 h-14 rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm"
                            :class="{
                              'bg-primary text-white':
                                node.status === OnboardingMapNodeStatusEnum.COMPLETED,
                              'bg-orange-500 text-white':
                                node.status === OnboardingMapNodeStatusEnum.IN_PROGRESS,
                              'bg-muted text-muted-foreground':
                                node.status === OnboardingMapNodeStatusEnum.NOT_STARTED
                            }"
                          >
                            <Icon :name="node.icon" class="w-7 h-7" />
                          </div>

                          <div class="flex-1 min-w-0">
                            <SheetTitle class="text-xl mb-3 leading-tight">{{
                              node.name
                            }}</SheetTitle>
                            <div class="flex items-center gap-3 mb-4">
                              <Badge
                                :variant="
                                  node.status === OnboardingMapNodeStatusEnum.COMPLETED
                                    ? 'default'
                                    : 'secondary'
                                "
                                class="text-xs px-2 py-1"
                              >
                                {{
                                  node.status === OnboardingMapNodeStatusEnum.COMPLETED
                                    ? 'Completed'
                                    : node.status === OnboardingMapNodeStatusEnum.IN_PROGRESS
                                      ? 'In Progress'
                                      : 'Not Started'
                                }}
                              </Badge>
                              <span class="text-sm text-muted-foreground font-medium">{{
                                getCompletedStatistics(node.id!)
                              }}</span>
                            </div>
                          </div>
                        </div>

                        <!-- 进度条和状态 - 移到图标下面对齐 -->
                        <div class="flex items-start gap-4 mt-4">
                          <div class="flex-1 space-y-3">
                            <div class="flex justify-between items-center text-sm">
                              <span class="text-muted-foreground">Overall Progress</span>
                              <span class="font-semibold text-primary"
                                >{{ getNodeProgressPercentage(node.id!) }}%</span
                              >
                            </div>
                            <Progress
                              :model-value="getNodeProgressPercentage(node.id!)"
                              class="h-2.5"
                            />
                          </div>
                        </div>

                        <!-- 节点描述 -->
                        <SheetDescription
                          v-if="node.description"
                          class="text-left mt-4 text-sm leading-relaxed"
                        >
                          {{ node.description }}
                        </SheetDescription>
                      </SheetHeader>
                      <!-- 任务列表 -->
                      <div class="flex-1 flex flex-col min-h-0 px-6">
                        <div class="flex items-center justify-between py-4 border-b">
                          <h4 class="font-semibold text-base">Learning Tasks</h4>
                          <span
                            class="text-sm text-muted-foreground bg-muted px-2 py-1 rounded-full"
                            >{{ node.tasks.length }} items</span
                          >
                        </div>

                        <ScrollArea class="flex-1 py-4">
                          <div class="space-y-4">
                            <div
                              v-for="(task, index) in node.tasks"
                              :key="index"
                              class="group relative bg-card border rounded-xl p-5 hover:shadow-lg hover:shadow-primary/10 transition-all duration-300 cursor-pointer"
                              :class="{
                                'border-primary bg-primary/5 shadow-sm':
                                  task.status === OnboardingMapTaskStatusEnum.COMPLETED,
                                'border-orange-500 bg-orange-50 shadow-sm':
                                  task.status === OnboardingMapTaskStatusEnum.IN_PROGRESS,
                                'hover:border-primary/50 hover:bg-primary/5':
                                  task.status === OnboardingMapTaskStatusEnum.NOT_STARTED
                              }"
                              @click="navigateToTaskDetail(task)"
                            >
                              <div class="flex items-start gap-4">
                                <!-- 状态指示器 -->
                                <div class="flex-shrink-0 mt-0.5">
                                  <div
                                    class="w-6 h-6 rounded-full flex items-center justify-center shadow-sm"
                                    :class="{
                                      'bg-primary text-white':
                                        task.status === OnboardingMapTaskStatusEnum.COMPLETED,
                                      'bg-orange-500 text-white':
                                        task.status === OnboardingMapTaskStatusEnum.IN_PROGRESS,
                                      'bg-muted text-muted-foreground':
                                        task.status === OnboardingMapTaskStatusEnum.NOT_STARTED
                                    }"
                                  >
                                    <Icon
                                      v-if="task.status === OnboardingMapTaskStatusEnum.COMPLETED"
                                      name="Check"
                                      class="w-3.5 h-3.5"
                                    />
                                    <Icon
                                      v-else-if="
                                        task.status === OnboardingMapTaskStatusEnum.IN_PROGRESS
                                      "
                                      name="Clock"
                                      class="w-3.5 h-3.5"
                                    />
                                    <Icon v-else name="Play" class="w-3.5 h-3.5" />
                                  </div>
                                </div>

                                <div class="flex-1 min-w-0">
                                  <div class="flex items-start justify-between mb-3">
                                    <h5
                                      class="font-semibold text-base leading-tight group-hover:text-primary transition-colors pr-2"
                                    >
                                      {{ task.bizName }}
                                    </h5>
                                    <Icon
                                      name="ExternalLink"
                                      class="w-4 h-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-all duration-200 ml-2 flex-shrink-0 transform group-hover:scale-110"
                                    />
                                  </div>

                                  <!-- 任务描述 -->
                                  <p
                                    v-if="task.description"
                                    class="text-sm text-muted-foreground mb-4 line-clamp-2 leading-relaxed"
                                  >
                                    {{ task.description }}
                                  </p>

                                  <div class="flex items-center justify-between">
                                    <div class="flex flex-wrap gap-2">
                                      <Badge variant="outline" class="text-xs px-2 py-1">
                                        {{
                                          contentTypes.find(
                                            (_contentType: {
                                              label: string
                                              value: OnboardingMapTaskTypeEnum
                                            }) => task.bizType === _contentType.value
                                          )?.label
                                        }}
                                      </Badge>
                                      <Badge
                                        v-if="task.mandatory"
                                        variant="destructive"
                                        class="text-xs px-2 py-1"
                                      >
                                        Mandatory
                                      </Badge>
                                      <Badge
                                        v-if="task.duration"
                                        variant="secondary"
                                        class="text-xs px-2 py-1"
                                      >
                                        {{ formatDuration(task.duration) }}
                                      </Badge>
                                    </div>

                                    <Badge
                                      :variant="
                                        task.status === OnboardingMapTaskStatusEnum.COMPLETED
                                          ? 'default'
                                          : 'secondary'
                                      "
                                      class="text-xs px-2 py-1 font-medium"
                                    >
                                      {{
                                        task.status === OnboardingMapTaskStatusEnum.COMPLETED
                                          ? 'Completed'
                                          : task.status === OnboardingMapTaskStatusEnum.IN_PROGRESS
                                            ? 'In Progress'
                                            : 'Not Started'
                                      }}
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </ScrollArea>
                      </div>
                      <SheetFooter class="border-t bg-muted/20 px-6 py-4 mt-auto">
                        <div class="flex items-center gap-3 w-full">
                          <SheetClose as-child class="flex-1">
                            <Button variant="outline" size="sm" class="w-full px-4 py-2">
                              <Icon name="X" class="me-2" :size="16" />
                              Close
                            </Button>
                          </SheetClose>
                          <Button
                            v-if="node.status === OnboardingMapNodeStatusEnum.IN_PROGRESS"
                            @click="completeOnboardingNode(node)"
                            size="sm"
                            class="flex-1 w-full px-4 py-2"
                          >
                            <Icon name="Check" class="me-2" :size="16" />
                            Mark as Complete
                          </Button>
                        </div>
                      </SheetFooter>
                    </SheetContent>
                  </Sheet>
                  <StepperDescription
                    v-if="
                      !(groupIndex === 0 && index === 0) &&
                      !(
                        groupIndex === Object.values(nodeGroupMap).length - 1 &&
                        index === nodes.length - 1
                      )
                    "
                    class="space-y-2"
                  >
                    <p class="text-nowrap">
                      {{
                        node.status === OnboardingMapNodeStatusEnum.COMPLETED
                          ? 'Completed'
                          : 'In Progress'
                      }}
                      {{ getCompletedStatistics(node.id) }}</p
                    >
                    <Badge
                      variant="outline"
                      v-if="node.status === OnboardingMapNodeStatusEnum.COMPLETED"
                      class="cursor-default py-1"
                    >
                      <Icon name="CircleCheck" class="me-1" :size="14" />
                      completed
                    </Badge>

                    <Button
                      :disabled="
                        onboardingMap.mode === OnboardingMapModeEnum.STAGE &&
                        getPreviousNodeStatus(node.id) !== OnboardingMapNodeStatusEnum.COMPLETED
                      "
                      :step="node.sort"
                      size="sm"
                      class="rounded-full"
                      @click="completeOnboardingNode(node)"
                      v-if="node.status === OnboardingMapNodeStatusEnum.IN_PROGRESS"
                    >
                      <Icon name="Check" :size="14" />
                      Complete
                    </Button>
                  </StepperDescription>
                </div>
              </StepperTrigger>
              <!--        <StepperSeparator class="w-full h-px group-data-disabled:bg-primary" :class="{'bg-primary': groupIndex === Object.values(nodeGroupMap).length - 1 && index === nodes.length - 1}" />-->
              <StepperLine
                :class="{
                  'bg-muted':
                    groupIndex === Object.values(nodeGroupMap).length - 1 &&
                    index === nodes.length - 1
                }"
              />
            </StepperItem>
            <!--奇数组 第2、4、6行-->
            <template v-if="groupIndex % 2 !== 0">
              <div class="absolute right-0 top-0 h-1/2 bg-foreground/30 w-[2px]"></div>
              <div class="absolute left-0 bottom-0 h-1/2 bg-foreground/30 w-[2px]"></div>
            </template>
            <!--偶数组 第1、3、5行-->
            <template v-else>
              <div
                v-if="groupIndex !== Object.values(nodeGroupMap).length - 1"
                class="absolute right-0 bottom-0 h-1/2 bg-foreground/30 w-[2px]"
              ></div>
              <div
                v-if="groupIndex !== 0"
                class="absolute left-0 top-0 h-1/2 bg-foreground/30 w-[2px]"
              ></div>
            </template>
          </Stepper>
        </div>
      </ContainerScroll>
    </template>
  </ContainerWrapper>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  OnboardingMapApi,
  OnboardingMapModeEnum,
  OnboardingMapNode,
  OnboardingMapNodeStatusEnum,
  OnboardingMapTaskStatusEnum,
  OnboardingMapTaskTypeEnum,
  OnboardingMapVO
} from '@/api/learning/onboardingmap'
import StepperLine from '@/views/learning/onboardingmap/components/StepperLine.vue'
import { ContainerWrapper, ContainerScroll } from '@/components/ContainerWrap'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<OnboardingMapVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数

type NodeGroupMap = Record<number, OnboardingMapNode[]>

const nodeGroupMap = ref<NodeGroupMap>({})
const onboardingMap = ref<OnboardingMapVO>({})
const selectedNodeId = ref(0)

const step = ref(0)

const contentTypes = [
  {
    label: 'COURSE',
    value: OnboardingMapTaskTypeEnum.ONLINE_COURSE
  },
  {
    label: 'ONBOARDING',
    value: OnboardingMapTaskTypeEnum.ONBOARDING
  },
  {
    label: 'ORIENTATION',
    value: OnboardingMapTaskTypeEnum.ORIENTATION
  },
  {
    label: 'COMPANY POLICY',
    value: OnboardingMapTaskTypeEnum.COMPANY_POLICY
  },
  {
    label: 'MLC TRAINING',
    value: OnboardingMapTaskTypeEnum.MLC_TRAINING
  }
]

const sheetShow = ref(false)

const getCompletedStatistics = (nodeId: number): string => {
  let totalTasks = 0
  let completedTasks = 0

  // 遍历所有分组
  for (const group of Object.values(nodeGroupMap.value)) {
    // 查找当前分组中是否包含目标 node
    const targetNode = group.find((node) => node.id === nodeId)

    if (targetNode) {
      // 如果找到对应节点，统计任务
      if (targetNode.tasks && Array.isArray(targetNode.tasks)) {
        totalTasks = targetNode.tasks.length
        completedTasks = targetNode.tasks.filter(
          (task) => task.status === OnboardingMapTaskStatusEnum.COMPLETED
        ).length
      }
      break // 找到后退出循环
    }
  }

  return `${completedTasks}/${totalTasks}`
}

// 统计方法
const getCompletedNodesCount = () => {
  return (
    onboardingMap.value?.nodes?.filter(
      (node) => node.status === OnboardingMapNodeStatusEnum.COMPLETED
    ).length || 0
  )
}

const getInProgressNodesCount = () => {
  return (
    onboardingMap.value?.nodes?.filter(
      (node) => node.status === OnboardingMapNodeStatusEnum.IN_PROGRESS
    ).length || 0
  )
}

const getTotalNodesCount = () => {
  return onboardingMap.value?.nodes?.length || 0
}

const getOverallProgressPercentage = () => {
  const total = getTotalNodesCount()
  if (total === 0) return 0
  return Math.round((getCompletedNodesCount() / total) * 100)
}

const getNodeProgressPercentage = (nodeId: number) => {
  const node = onboardingMap.value?.nodes?.find((node) => node.id === nodeId)
  if (!node || !node.tasks || node.tasks.length === 0) return 0
  const completedTasks = node.tasks.filter(
    (task) => task.status === OnboardingMapTaskStatusEnum.COMPLETED
  )
  return Math.round((completedTasks.length / node.tasks.length) * 100)
}

const formatDuration = (seconds: number) => {
  if (!seconds) return ''
  if (seconds < 60) return `${seconds}s`
  const minutes = Math.floor(seconds / 60)
  if (minutes < 60) return `${minutes}m`
  const hours = Math.floor(minutes / 60)
  return `${hours}h ${minutes % 60}m`
}

// 跳转到任务详情页面
const navigateToTaskDetail = (task: any) => {
  console.log('Navigating to task:', task)

  // 根据任务类型跳转到不同的页面
  switch (task.bizType) {
    case OnboardingMapTaskTypeEnum.ONLINE_COURSE:
      // 跳转到课程详情页
      router.open(`/content/detail/${task.bizId}`, '_blank')
      break
    case OnboardingMapTaskTypeEnum.ONBOARDING:
      // 跳转到Onboarding详情页
      window.open(`/onboarding/detail/${task.bizId}`, '_blank')
      break
    case OnboardingMapTaskTypeEnum.ORIENTATION:
      // 跳转到考试页面
      window.open(`/orientation/detail/${task.bizId}`, '_blank')
      break
    case OnboardingMapTaskTypeEnum.COMPANY_POLICY:
      // 跳转到公司政策页面
      window.open(`/policy/detail/${task.bizId}`, '_blank')
      break
    case OnboardingMapTaskTypeEnum.MLC_TRAINING:
      // 跳转到MLC培训页面
      window.open(`/training/detail?id=${task.bizId}`, '_blank')
      break
    default:
      break
  }
}

/**
 * 获取上一个节点的状态
 * @param nodeId 当前节点ID
 * @returns 上一个节点的状态，如果没有上一个节点则返回null
 */
const getPreviousNodeStatus = (nodeId: number): OnboardingMapNodeStatusEnum | null => {
  // 遍历所有分组
  for (const group of Object.values(nodeGroupMap.value)) {
    // 查找当前分组中的节点索引
    const currentIndex = group.findIndex((node) => node.id === nodeId)

    if (currentIndex > -1) {
      // 如果是第一个节点，则没有上一个节点
      if (currentIndex === 0) {
        return null
      }

      // 返回上一个节点的状态
      return group[currentIndex - 1].status
    }
  }

  // 没有找到节点，返回null
  return null
}

const handleNodeShow = (nodeId: number) => {
  selectedNodeId.value = nodeId
  if (!sheetShow.value) sheetShow.value = true
}

/** 查询列表 */
const getOnboardingMap = async () => {
  loading.value = true
  try {
    onboardingMap.value = await OnboardingMapApi.getOnboardingMap()
    // 初始化 timelineMap
    onboardingMap.value.nodes.forEach((_node, index) => {
      const groupIndex = Math.floor(index / 4)
      if (!nodeGroupMap.value[groupIndex]) {
        nodeGroupMap.value[groupIndex] = []
      }
      nodeGroupMap.value[groupIndex].push(_node)
    })

    // 动态设置 position 属性
    Object.keys(nodeGroupMap.value).forEach((groupKey) => {
      const groupIndex = parseInt(groupKey, 10)
      const isOddGroup = groupIndex % 2 !== 0 // 判断是否为奇数组

      nodeGroupMap.value[groupIndex].forEach((_node, stepIndex) => {
        if (!isOddGroup) {
          // 偶数组：25%, 50%, 75%
          _node.position = (stepIndex + 1) * 25 + ''
        } else {
          // 奇数组：75%, 50%, 25%
          _node.position = (3 - stepIndex) * 25 + ''
        }
        _node.icon = 'BookOpen'
      })
      // 第一组前面加一个自定义step，最后一组后面加一个自定义step
      if (groupIndex === 0) {
        nodeGroupMap.value[groupIndex].unshift({
          id: 0,
          mapId: 0,
          name: '',
          icon: 'SendHorizontal',
          sort: 0,
          status: 90,
          tasks: []
        })
      }

      if (groupIndex === Object.keys(nodeGroupMap.value).length - 1) {
        nodeGroupMap.value[groupIndex].push({
          id: 0,
          mapId: 0,
          name: '',
          icon: 'ShieldCheck',
          sort: onboardingMap.value.nodes.length + 1,
          status: 10,
          tasks: []
        })
      }

      const onboardingMapNode = onboardingMap.value.nodes.find(
        (node, index) => index !== 0 && node.status === OnboardingMapNodeStatusEnum.COMPLETED
      )
      if (onboardingMapNode) {
        step.value = onboardingMapNode.sort
      }
    })
  } finally {
    loading.value = false
  }
}

const completeOnboardingNode = async (node: OnboardingMapNode) => {
  try {
    const confirmed = await message.confirm(
      'Are you sure you want to mark this stage as completed?',
      'Complete Stage'
    )

    if (confirmed) {
      try {
        await OnboardingMapApi.completeOnboardingNode(node.id!)
        node.status = OnboardingMapNodeStatusEnum.COMPLETED
        message.success('Stage completed successfully!')
      } catch (error) {
        console.error('Error completing node:', error)
        // message.error('Failed to complete stage. Please try again.')
      }
    }
  } catch (e) {
    console.error('Error completing node:', e)
  }
}

onMounted(() => {
  getOnboardingMap()
})
</script>
