<template>
  <div class="flex items-center py-2 rounded">
    <!-- <Button
      :disabled="breadcrumbs.length < 2"
      variant="ghost"
      size="icon"
      class="shrink-0 w-8 h-8 -ms-2 me-1"
      @click="handleItemClick(breadcrumbs[breadcrumbs.length - 2].id, true)"
    >
      <ArrowLeft class="w-4 h-4" />
    </Button>
    <Breadcrumb>
      <BreadcrumbList>
        <template v-for="(item, index) in breadcrumbs" :key="item.id">
          <BreadcrumbItem>
            <BreadcrumbLink
              as="button"
              class="flex items-center space-x-1 text-sm"
              @click="handleItemClick(item.id, index !== breadcrumbs.length - 1)"
            >
              <HomeIcon v-if="index === 0" class="w-4 h-4" />
              {{ item.name }}
            </BreadcrumbLink>
          </BreadcrumbItem>

          <BreadcrumbSeparator v-if="index !== breadcrumbs.length - 1" />
        </template>
      </BreadcrumbList>
    </Breadcrumb> -->
  </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps } from 'vue'
import { ArrowRight, ArrowLeft, HomeIcon } from 'lucide-vue-next'
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb' // 这里根据你的 `shadcn-vue` 组件路径调整

defineOptions({ name: 'Breadcrumb' })

interface BreadcrumbItem {
  id: number
  name: string
}

const props = defineProps<{ breadcrumbs: BreadcrumbItem[] }>()

const emit = defineEmits(['item-click'])

const handleItemClick = (id: number, change: boolean) => {
  if (change) {
    emit('item-click', id)
  }
}
</script>
